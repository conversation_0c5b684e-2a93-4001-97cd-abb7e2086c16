// ===== Archivo: src\app\auth\callback\page.tsx (CORREGIDO Y SIMPLIFICADO) =====
'use client';

import { useEffect, useState, Suspense, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/supabaseClient';

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Procesando autenticación, por favor espera...');
  const [logMessages, setLogMessages] = useState<string[]>([]);
  const timeoutIdRef = useRef<NodeJS.Timeout | null>(null);

  const log = (msg: string, data?: any) => {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${msg}`;
    console.log(logEntry, data || '');
    setLogMessages(prev => [...prev, logEntry]);
  };

  useEffect(() => {
    log('--- AuthCallbackContent useEffect INICIADO ---');
    log('URL Completa en el cliente:', window.location.href);

    const errorDescription = searchParams.get('error_description');
    if (errorDescription) {
      log('❌ Error explícito en la URL.', { error: errorDescription });
      setStatus('error');
      setMessage(decodeURIComponent(errorDescription));
      return;
    }

    const supabase = createClient();
    log('Cliente Supabase del navegador inicializado.');

    // Verificar inmediatamente si ya hay una sesión activa
    const checkInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        log('🔍 Verificación inicial de sesión:', {
          hasSession: !!session,
          userId: session?.user?.id,
          error: error?.message
        });

        if (session?.user && status === 'loading') {
          log('✅ Sesión activa encontrada inmediatamente. Redirigiendo...');
          setStatus('success');
          setMessage('¡Sesión encontrada! Redirigiendo...');
          router.push('/app');
          return true;
        }
        return false;
      } catch (error) {
        log('❌ Error verificando sesión inicial:', error);
        return false;
      }
    };

    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      log(`EVENTO onAuthStateChange RECIBIDO: ${event}`, {
        hasSession: !!session,
        userId: session?.user?.id,
        userEmail: session?.user?.email
      });

      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
        timeoutIdRef.current = null;
      }

      if (event === 'SIGNED_IN' && session?.user) {
        log('✅ Evento SIGNED_IN detectado con magic link. Redirigiendo a /app...');
        setStatus('success');
        setMessage('¡Acceso exitoso! Redirigiendo...');
        router.push('/app');
      } else if (event === 'PASSWORD_RECOVERY') {
        log('✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password...');
        setStatus('success');
        setMessage('Sesión de recuperación lista. Redirigiendo...');
        router.push('/auth/reset-password');
      } else if (event === 'USER_UPDATED' && session?.user) {
        log('✅ Evento USER_UPDATED con sesión. Redirigiendo a /app...');
        setStatus('success');
        setMessage('Cuenta actualizada. Redirigiendo...');
        router.push('/app');
      } else if (event === 'INITIAL_SESSION' && session?.user) {
        log('✅ Evento INITIAL_SESSION con sesión válida. Redirigiendo a /app...');
        setStatus('success');
        setMessage('¡Sesión encontrada! Redirigiendo...');
        router.push('/app');
      } else if (event === 'INITIAL_SESSION' && !session) {
        log('🔹 Evento INITIAL_SESSION sin sesión. Esperando más eventos...');
        // Verificar inmediatamente si hay una sesión
        setTimeout(async () => {
          const { data: { session: currentSession } } = await supabase.auth.getSession();
          if (currentSession?.user && status === 'loading') {
            log('✅ Sesión encontrada en verificación posterior. Redirigiendo...');
            setStatus('success');
            setMessage('¡Sesión encontrada! Redirigiendo...');
            router.push('/app');
          }
        }, 1000);
      }
    });

    log('Listener onAuthStateChange configurado. Esperando evento...');

    // Verificar inmediatamente si ya hay una sesión
    checkInitialSession();

    // Iniciar un timeout de seguridad más largo (10-15s) para manejar casos raros
    timeoutIdRef.current = setTimeout(() => {
      if (status === 'loading') {
        log('❌ TIMEOUT (10s): No se recibió un evento de autenticación válido.');
        setStatus('error');
        setMessage('El enlace de autenticación es inválido, ha expirado o ya fue utilizado. Por favor, intenta de nuevo.');
      }
    }, 10000); // 10 segundos

    return () => {
      log('--- AuthCallbackContent useEffect LIMPIEZA ---');
      authListener?.subscription.unsubscribe();
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router]);

  // ... (resto del componente JSX sin cambios)
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center">
        {status === 'loading' && (
          <>
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Procesando...</h2>
            <p className="text-gray-600">{message}</p>
          </>
        )}

        {status === 'success' && (
          <>
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">¡Éxito!</h2>
            <p className="text-gray-600">{message}</p>
          </>
        )}

        {status === 'error' && (
          <>
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Error de Autenticación</h2>
            <p className="text-gray-600 mb-4">{message}</p>
            <div className="space-y-2">
              <button
                onClick={() => router.push('/login')}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
              >
                Ir a Login
              </button>
            </div>
          </>
        )}


        <div className="mt-6 pt-4 border-t border-gray-200 text-left bg-gray-50 p-3 rounded max-h-48 overflow-y-auto">
            <h4 className="text-sm font-semibold text-gray-700 mb-2">Registro de Eventos:</h4>
            <div className="text-xs text-gray-500 space-y-1">
                {logMessages.map((logMsg, index) => <div key={index}>{logMsg}</div>)}
            </div>
        </div>
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Cargando...</h2>
          <p className="text-gray-600">Procesando autenticación...</p>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  );
}