import { prepararDocumentos } from './geminiClient';
import { PROMPT_TESTS } from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';
import { type Chunk } from '@/lib/utils/textProcessing';
import { seleccionarChunksRelevantes } from '@/lib/utils/chunkSelector';
import * as Sentry from "@sentry/nextjs";

/**
 * Genera un test con preguntas de opción múltiple a partir de los documentos
 */
export async function generarTest(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  cantidad: number = 10,
  instrucciones?: string
): Promise<{
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}[]> {
  try {
    // Preparar el contenido de los documentos
    const resultadoDocumentos = prepararDocumentos(documentos);

    // Verificar si se usó chunking
    if (resultadoDocumentos.wasChunked && Array.isArray(resultadoDocumentos.content)) {
      console.log(`🧪 Procesando test con chunking: ${resultadoDocumentos.content.length} chunks`);
      return await procesarTestConChunks(resultadoDocumentos.content, cantidad, instrucciones, documentos);
    } else {
      // Procesamiento tradicional para documentos sin chunking
      const contenidoDocumentos = Array.isArray(resultadoDocumentos.content)
        ? resultadoDocumentos.content.join('\n\n')
        : resultadoDocumentos.content;

      if (!contenidoDocumentos) {
        throw new Error("No se han proporcionado documentos para generar el test.");
      }

      console.log(`🧪 Procesando test sin chunking`);
      return await procesarTestSinChunks(contenidoDocumentos, cantidad, instrucciones);
    }


  } catch (error) {
    console.error('Error al generar test:', error);
    throw error;
  }
}

/**
 * Procesa test con chunking - selecciona chunks relevantes y los combina en un solo contexto
 */
async function procesarTestConChunks(
  chunks: Chunk[],
  cantidad: number,
  instrucciones?: string,
  documentos?: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): Promise<{
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}[]> {
  // --- INICIO DE LA NUEVA LÓGICA DE SELECCIÓN ---
  console.log(`🧪 Seleccionando chunks relevantes para la petición: "${instrucciones}"`);
  const selectionResult = seleccionarChunksRelevantes(chunks, instrucciones || '');

  if (selectionResult.selectedChunks.length === 0) {
    throw new Error("No se pudo encontrar contenido relevante para tu petición. Intenta ser más específico.");
  }

  console.log(`🧪 Chunks relevantes encontrados: ${selectionResult.selectedChunks.length} de ${chunks.length}`);
  // --- FIN DE LA NUEVA LÓGICA DE SELECCIÓN ---

  // PASO 2 (NUEVO): Combinar el contenido de los chunks seleccionados en un solo contexto
  console.log(`🔗 Uniendo ${selectionResult.selectedChunks.length} chunks en un solo contexto.`);
  const combinedContent = selectionResult.selectedChunks
    .map(chunk => chunk.content)
    .join('\n\n---\n\n'); // Un separador claro ayuda a la IA

  // PASO 3: Preparar el prompt para una ÚNICA llamada
  let prompt = PROMPT_TESTS
    .replace('{documentos}', combinedContent) // Usar el contenido combinado
    .replace('{cantidad}', cantidad.toString());

  if (instrucciones) {
    prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
  } else {
    prompt = prompt.replace('{instrucciones}', '');
  }

  // PASO 4: Hacer UNA SOLA llamada a la IA
  const config = getOpenAIConfig('TESTS');
  const messages = [{ role: 'user' as const, content: prompt }];
  const responseText = await llamarOpenAI(messages, {
    ...config,
    activityName: `Generación [Combinada] de Test (${cantidad} preguntas)`
  });

  // PASO 5: Parsear la respuesta directamente (ya no se necesita combinar)
  const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);
  if (!jsonMatch) {
    throw new Error("La IA no devolvió un formato JSON válido.");
  }

  const testJson = jsonMatch[0];

  // Corregir errores comunes de formato antes del parsing
  const testJsonCorregido = testJson
    .replace(/"opcion([abcd])"/g, '"opcion_$1"')
    .replace(/"opciona"/g, '"opcion_a"')
    .replace(/"opcionb"/g, '"opcion_b"')
    .replace(/"opcionc"/g, '"opcion_c"')
    .replace(/"opciond"/g, '"opcion_d"');

  const preguntas = JSON.parse(testJsonCorregido);

  // Validar formato básico
  if (!Array.isArray(preguntas) || preguntas.length === 0) {
    throw new Error("La IA no generó preguntas válidas.");
  }

  // Validar estructura de cada pregunta
  const preguntasValidas = preguntas.filter(pregunta =>
    pregunta.pregunta &&
    pregunta.opcion_a &&
    pregunta.opcion_b &&
    pregunta.opcion_c &&
    pregunta.opcion_d &&
    pregunta.respuesta_correcta
  );

  if (preguntasValidas.length === 0) {
    throw new Error("Ninguna de las preguntas generadas tiene el formato correcto.");
  }

  console.log(`🧪 Test generado con chunking combinado: ${preguntasValidas.length} preguntas finales`);
  return preguntasValidas;
}

/**
 * Procesa test sin chunking - método tradicional
 */
async function procesarTestSinChunks(
  contenidoDocumentos: string,
  cantidad: number,
  instrucciones?: string
): Promise<{
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}[]> {
  // Construir el prompt para la IA usando el prompt personalizado
  let prompt = PROMPT_TESTS
    .replace('{documentos}', contenidoDocumentos)
    .replace('{cantidad}', cantidad.toString());

  // Añadir instrucciones adicionales si se proporcionan
  if (instrucciones) {
    prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
  } else {
    prompt = prompt.replace('{instrucciones}', '');
  }

  // Obtener configuración específica para tests
  const config = getOpenAIConfig('TESTS');

  console.log(`🧪 Generando test con modelo: ${config.model} (max_tokens: ${config.max_tokens})`);

  // Generar el test usando OpenAI con la configuración correcta
  const messages = [{ role: 'user' as const, content: prompt }];
  const responseText = await llamarOpenAI(messages, {
    ...config,
    activityName: `Generación de Test (${cantidad || 'N/A'} preguntas)`
  });

  // Extraer el JSON de la respuesta
  const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);

  if (!jsonMatch) {
    console.log('❌ No se encontró JSON en la respuesta. Respuesta recibida:', responseText.substring(0, 500));
    throw new Error("No se pudo extraer el formato JSON de la respuesta.");
  }

  const testJson = jsonMatch[0];

  // Corregir errores comunes de formato antes del parsing
  const testJsonCorregido = testJson
    .replace(/"opcion([abcd])"/g, '"opcion_$1"')  // Corregir opcionX -> opcion_X
    .replace(/"opciona"/g, '"opcion_a"')
    .replace(/"opcionb"/g, '"opcion_b"')
    .replace(/"opcionc"/g, '"opcion_c"')
    .replace(/"opciond"/g, '"opcion_d"');

  const preguntas = JSON.parse(testJsonCorregido);

  // Verificar la cantidad de preguntas generadas
  console.log(`📊 Preguntas generadas: ${preguntas.length} de ${cantidad} solicitadas`);

  // Validar el formato
  if (!Array.isArray(preguntas) || preguntas.length === 0) {
    throw new Error("El formato de las preguntas generadas no es válido.");
  }

  if (preguntas.length !== cantidad) {
    console.log(`⚠️ Advertencia: Se generaron ${preguntas.length} preguntas en lugar de ${cantidad}`);
  }

  // Validar que cada pregunta tiene el formato correcto
  preguntas.forEach((pregunta: any, index: number) => {
    if (!pregunta.pregunta || !pregunta.opcion_a || !pregunta.opcion_b ||
        !pregunta.opcion_c || !pregunta.opcion_d || !pregunta.respuesta_correcta) {
      throw new Error(`La pregunta ${index + 1} no tiene el formato correcto.`);
    }

    // Asegurarse de que la respuesta correcta es una de las opciones válidas
    if (!['a', 'b', 'c', 'd'].includes(pregunta.respuesta_correcta)) {
      throw new Error(`La respuesta correcta de la pregunta ${index + 1} no es válida.`);
    }
  });

  return preguntas;
}
