// src/lib/gemini/resumenGenerator.ts
// NINGÚN CAMBIO NECESARIO AQUÍ. EL CÓDIGO ESTÁ CORRECTO.

import { SummaryOrchestrator } from '../services/summaryOrchestrator';
import { PROMPT_RESUMEN_FINAL_CONSOLIDACION } from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';
import { type Documento } from '@/types';
import { CHUNKING_LIMITS } from '@/config/chunking';

/**
 * Genera un resumen de un documento, actuando como un despachador que elige
 * entre un procesamiento directo (para documentos cortos) o un flujo Map-Reduce
 * orquestado (para documentos largos).
 */
export async function generarResumen(
  documento: Documento, // La clave es que el objeto que se pase aquí debe ser un 'Documento' completo
  instrucciones?: string
): Promise<string> {
  try {
    // 1. Validar entrada
    if (!documento || !documento.contenido || documento.contenido.trim().length === 0) {
      throw new Error("El documento proporcionado no es válido o está vacío.");
    }

    // 2. Decidir la estrategia: Vía rápida vs. Orquestador
    const umbralParaChunking = CHUNKING_LIMITS.MIN_SIZE_FOR_CHUNKING || 20000;

    if (documento.contenido.length < umbralParaChunking) {
      // --- VÍA RÁPIDA PARA DOCUMENTOS CORTOS ---
      console.log(`⚡️ Documento corto detectado. Usando vía rápida para resumen.`);
      
      const prompt = PROMPT_RESUMEN_FINAL_CONSOLIDACION
        .replace('{texto_largo_combinado}', documento.contenido);
      
      const config = getOpenAIConfig('RESUMENES');
      const messages = [{ role: 'user' as const, content: prompt }];

      const resumenDirecto = await llamarOpenAI(messages, {
        ...config,
        activityName: 'Resumen (Vía Rápida)'
      });

      console.log('✅ Resumen (Vía Rápida) generado exitosamente.');
      return resumenDirecto;

    } else {
      // --- FLUJO ORQUESTADO PARA DOCUMENTOS LARGOS ---
      console.log(`🧠 Documento largo detectado. Delegando a SummaryOrchestrator.`);
      
      return await SummaryOrchestrator.generateHierarchicalSummary(documento, instrucciones);
    }

  } catch (error) {
    console.error('Error en el despachador de generarResumen:', error);
    if (error instanceof Error) {
      throw new Error(`Error al generar el resumen: ${error.message}`);
    }
    throw new Error("Ha ocurrido un error inesperado durante la generación del resumen.");
  }
}