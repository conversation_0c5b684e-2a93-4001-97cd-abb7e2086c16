// src/app/api/notify-signup/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

// Interfaces para diferentes tipos de notificación
interface SubscriptionRequestData {
  type: 'subscription_request';
  email: string;
  customerName?: string;
  planName: string;
}

interface SubscriptionCancelledData {
  type: 'subscription_cancelled';
  userEmail: string;
  userName?: string;
  subscriptionPlan: string;
  periodEnd: string;
}

type NotificationData = SubscriptionRequestData | SubscriptionCancelledData;

export async function POST(request: NextRequest) {
  try {
    const data: NotificationData = await request.json();

    console.log('Using Resend API key starting with:', process.env.RESEND_API_KEY?.substring(0, 10) + '...');
    console.log('Sending notification to:', process.env.NOTIFICATION_EMAIL);

    // Validar campos requeridos según el tipo
    if (data.type === 'subscription_request') {
      if (!data.email || !data.planName) {
        return NextResponse.json(
          { error: 'Email y planName son requeridos para subscription_request' },
          { status: 400 }
        );
      }
    } else if (data.type === 'subscription_cancelled') {
      if (!data.userEmail || !data.subscriptionPlan || !data.periodEnd) {
        return NextResponse.json(
          { error: 'userEmail, subscriptionPlan y periodEnd son requeridos para subscription_cancelled' },
          { status: 400 }
        );
      }
    } else {
      return NextResponse.json(
        { error: 'Tipo de notificación no válido' },
        { status: 400 }
      );
    }

    let emailData;

    if (data.type === 'subscription_cancelled') {
      // Email para cancelación de suscripción
      emailData = {
        from: 'OposiAI Notificaciones <<EMAIL>>',
        to: [process.env.NOTIFICATION_EMAIL!],
        subject: `❌ Suscripción Cancelada OposiAI: ${data.userName || 'Usuario'}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc2626;">Suscripción Cancelada</h2>

            <div style="background-color: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #991b1b;">Detalles de la Cancelación</h3>
              <p><strong>Email:</strong> ${data.userEmail}</p>
              <p><strong>Nombre:</strong> ${data.userName || 'No proporcionado'}</p>
              <p><strong>Plan Cancelado:</strong> ${data.subscriptionPlan}</p>
              <p><strong>Acceso hasta:</strong> ${data.periodEnd}</p>
              <p><strong>Fecha de Cancelación:</strong> ${new Date().toLocaleString('es-ES')}</p>
            </div>

            <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
              <h4 style="margin-top: 0; color: #92400e;">Información</h4>
              <p style="margin-bottom: 0;">El usuario mantendrá acceso a las funciones premium hasta el final de su período de facturación.</p>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                Este email fue generado automáticamente por el sistema de suscripciones de OposiAI.
              </p>
            </div>
          </div>
        `,
      };
    } else {
      // Email para solicitud de suscripción (comportamiento original)
      emailData = {
        from: 'OposiAI Notificaciones <<EMAIL>>',
        to: [process.env.NOTIFICATION_EMAIL!],
        subject: `🚀 Nueva Solicitud de Suscripción OposiAI: Plan ${data.planName}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2563eb;">Nueva Solicitud de Suscripción</h2>

            <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #1e40af;">Detalles del Cliente</h3>
              <p><strong>Email:</strong> ${data.email}</p>
              <p><strong>Nombre:</strong> ${data.customerName || 'No proporcionado'}</p>
              <p><strong>Plan Seleccionado:</strong> ${data.planName}</p>
              <p><strong>Fecha:</strong> ${new Date().toLocaleString('es-ES')}</p>
            </div>

            <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
              <h4 style="margin-top: 0; color: #92400e;">Acción Requerida</h4>
              <p style="margin-bottom: 0;">Por favor, añade manualmente este usuario a Supabase con el plan correspondiente.</p>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                Este email fue generado automáticamente por el sistema de suscripciones de OposiAI.
              </p>
            </div>
          </div>
        `,
      };
    }

    console.log('Attempting to send email with Resend...');
    console.log('Email payload:', {
      from: emailData.from,
      to: emailData.to,
      subject: emailData.subject
    });

    const emailResult = await resend.emails.send(emailData);

    console.log('Email enviado exitosamente:', emailResult);

    return NextResponse.json({
      success: true,
      message: 'Notificación enviada correctamente',
      emailData: emailResult
    });

  } catch (error) {
    console.error('Error enviando email con Resend:', error);
    
    return NextResponse.json(
      { 
        error: 'Error al enviar la notificación',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
