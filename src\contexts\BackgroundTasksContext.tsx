'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode, useMemo, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { taskPersistenceService } from '@/lib/services/TaskPersistenceService';

export interface BackgroundTask {
  id: string;
  type: 'mapa-mental' | 'test' | 'flashcards' | 'plan-estudios' | 'resumen';
  title: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress?: number;
  result?: any;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
}

interface BackgroundTasksContextType {
  tasks: BackgroundTask[];
  addTask: (task: Omit<BackgroundTask, 'id' | 'status' | 'createdAt'>) => string;
  updateTask: (id: string, updates: Partial<BackgroundTask>) => void;
  removeTask: (id: string) => void;
  getTask: (id: string) => BackgroundTask | undefined;
  getTasksByType: (type: BackgroundTask['type']) => BackgroundTask[];
  clearCompletedTasks: () => void;
  clearAllTasks: () => void; // Nueva función para limpiar todas las tareas
  activeTasks: BackgroundTask[];
  completedTasks: BackgroundTask[];
  isInitialized: boolean;
  getTaskStats: () => ReturnType<typeof taskPersistenceService.getTaskStats>;
}

const BackgroundTasksContext = createContext<BackgroundTasksContextType | undefined>(undefined);

interface BackgroundTasksProviderProps {
  children: ReactNode;
}

export const BackgroundTasksProvider: React.FC<BackgroundTasksProviderProps> = ({ children }) => {
  const [tasks, setTasks] = useState<BackgroundTask[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Cargar tareas desde localStorage al inicializar
  useEffect(() => {
    const loadPersistedTasks = () => {
      try {
        const persistedTasks = taskPersistenceService.loadTasks();
        if (persistedTasks.length > 0) {
          setTasks(persistedTasks);
          console.log(`🔄 Recuperadas ${persistedTasks.length} tareas persistidas`);
        }
      } catch (error) {
        console.error('Error al cargar tareas persistidas:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    loadPersistedTasks();
  }, []);

  // Persistir tareas cuando cambien (solo después de la inicialización)
  useEffect(() => {
    if (isInitialized) {
      taskPersistenceService.saveTasks(tasks);
    }
  }, [tasks, isInitialized]);

  const addTask = useCallback((taskData: Omit<BackgroundTask, 'id' | 'status' | 'createdAt'>) => {
    const id = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newTask: BackgroundTask = {
      ...taskData,
      id,
      status: 'pending',
      createdAt: new Date(),
    };

    setTasks(prev => [...prev, newTask]);

    // Mostrar notificación de inicio
    toast.loading(`Iniciando: ${newTask.title}`, {
      id: `task_start_${id}`,
      duration: 2000,
    });

    return id;
  }, []);

  const updateTask = useCallback((id: string, updates: Partial<BackgroundTask>) => {
    setTasks(prev => prev.map(task => {
      if (task.id === id) {
        const updatedTask = { ...task, ...updates };

        // Manejar notificaciones según el estado
        if (updates.status === 'processing' && task.status === 'pending') {
          toast.dismiss(`task_start_${id}`);
          toast.loading(`Procesando: ${task.title}`, {
            id: `task_processing_${id}`,
          });
        } else if (updates.status === 'completed' && task.status !== 'completed') {
          toast.dismiss(`task_processing_${id}`);
          toast.success(`Completado: ${task.title}`, {
            id: `task_completed_${id}`,
            duration: 4000,
          });
          updatedTask.completedAt = new Date();
        } else if (updates.status === 'error' && task.status !== 'error') {
          toast.dismiss(`task_processing_${id}`);
          toast.error(`Error: ${task.title}`, {
            id: `task_error_${id}`,
            duration: 5000,
          });
        }

        return updatedTask;
      }
      return task;
    }));
  }, []);

  const removeTask = useCallback((id: string) => {
    setTasks(prev => prev.filter(task => task.id !== id));
    // Limpiar notificaciones relacionadas
    toast.dismiss(`task_start_${id}`);
    toast.dismiss(`task_processing_${id}`);
    toast.dismiss(`task_completed_${id}`);
    toast.dismiss(`task_error_${id}`);
  }, []);

  const getTask = useCallback((id: string) => {
    return tasks.find(task => task.id === id);
  }, [tasks]);

  const getTasksByType = useCallback((type: BackgroundTask['type']) => {
    return tasks.filter(task => task.type === type);
  }, [tasks]);

  const clearCompletedTasks = useCallback(() => {
    setTasks(prev => {
      const activeTasks = prev.filter(task => task.status !== 'completed' && task.status !== 'error');
      console.log(`🧹 Limpiadas ${prev.length - activeTasks.length} tareas completadas`);
      return activeTasks;
    });
  }, []);

  const clearAllTasks = useCallback(() => {
    setTasks([]);
    taskPersistenceService.clearTasks();
    // Limpiar todas las notificaciones de tareas
    toast.dismiss();
    console.log('🗑️ Todas las tareas han sido limpiadas');
  }, []);

  const activeTasks = useMemo(() => tasks.filter(task =>
    task.status === 'pending' || task.status === 'processing'
  ), [tasks]);

  const completedTasks = useMemo(() => tasks.filter(task =>
    task.status === 'completed' || task.status === 'error'
  ), [tasks]);

  const getTaskStats = useCallback(() => {
    return taskPersistenceService.getTaskStats();
  }, []);

  const value: BackgroundTasksContextType = useMemo(() => ({
    tasks,
    addTask,
    updateTask,
    removeTask,
    getTask,
    getTasksByType,
    clearCompletedTasks,
    clearAllTasks,
    activeTasks,
    completedTasks,
    isInitialized,
    getTaskStats,
  }), [tasks, addTask, updateTask, removeTask, getTask, getTasksByType, clearCompletedTasks, clearAllTasks, activeTasks, completedTasks, isInitialized, getTaskStats]);

  return (
    <BackgroundTasksContext.Provider value={value}>
      {children}
    </BackgroundTasksContext.Provider>
  );
};

export const useBackgroundTasks = () => {
  const context = useContext(BackgroundTasksContext);
  if (context === undefined) {
    throw new Error('useBackgroundTasks must be used within a BackgroundTasksProvider');
  }
  return context;
};
