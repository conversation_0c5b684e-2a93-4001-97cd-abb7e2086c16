import { z } from 'zod';

export const preguntaFormSchema = z.object({
  pregunta: z.string().min(1, 'La pregunta es obligatoria').max(500, 'Máximo 500 caracteres'),
  documentos: z.array(
    z.object({
      id: z.string().min(1), // ID requerido para documentos
      titulo: z.string().min(1),
      contenido: z.string().nullable().optional(), // Puede ser null, undefined o string
      categoria: z.string().optional().nullable(),
      numero_tema: z.union([z.number().int().positive(), z.string(), z.null(), z.undefined()]).optional(),
      creado_en: z.string().optional(),
      actualizado_en: z.string().optional(),
      user_id: z.string().optional(),
      tipo_original: z.string().optional(),
      // Campos adicionales para Storage
      storage_path: z.string().optional(),
      file_size_bytes: z.number().optional(),
      content_type: z.string().optional(),
      contenido_corto: z.string().nullable().optional(), // Puede ser null, undefined o string
    })
  ).min(1, 'Debes seleccionar al menos un documento'),
});

// Esquema simplificado para los generadores que solo necesitan la petición
export const generatorFormSchema = z.object({
  peticion: z.string().min(1, 'La petición es obligatoria').max(500, 'Máximo 500 caracteres'),
});

// Esquema específico para tests que incluye la cantidad de preguntas
export const testFormSchema = z.object({
  peticion: z.string().min(1, 'La petición es obligatoria').max(500, 'Máximo 500 caracteres'),
  cantidad: z.number().min(1, 'Mínimo 1 pregunta').max(50, 'Máximo 50 preguntas').default(10),
});
// Esquema específico para flashcards que incluye la cantidad de tarjetas
export const flashcardFormSchema = z.object({
  peticion: z.string().min(1, 'La petición es obligatoria').max(500, 'Máximo 500 caracteres'),
  cantidad: z.number().min(1, 'Mínimo 1 flashcard').max(30, 'Máximo 30 flashcards').default(10),
});
export const mindMapFormSchema = generatorFormSchema;

// Esquema para reseteo de contraseña
export const forgotPasswordSchema = z.object({
  email: z.string()
    .min(1, 'El email es obligatorio')
    .email('Por favor, ingresa un email válido')
    .max(255, 'El email es demasiado largo'),
});

// Esquema para actualizar contraseña (reutilizable)
export const updatePasswordSchema = z.object({
  password: z.string()
    .min(8, 'La contraseña debe tener al menos 8 caracteres')
    .max(128, 'La contraseña es demasiado larga'),
  confirmPassword: z.string(),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Las contraseñas no coinciden',
  path: ['confirmPassword'], // Para mostrar error en el campo de confirmación
});
