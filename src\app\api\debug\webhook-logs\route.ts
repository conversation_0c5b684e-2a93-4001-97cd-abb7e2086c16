// src/app/api/debug/webhook-logs/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { SupabaseAdminService, supabaseAdmin } from '@/lib/supabase/admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('session_id');
    const email = searchParams.get('email');

    console.log('🔍 Debug webhook logs - sessionId:', sessionId, 'email:', email);

    // 1. Verificar transacciones en la base de datos
    let transactions = [];
    if (sessionId) {
      const transaction = await SupabaseAdminService.getTransactionBySessionId(sessionId);
      transactions = transaction ? [transaction] : [];
    } else if (email) {
      // Buscar transacciones por email
      const { data, error } = await supabaseAdmin
        .from('stripe_transactions')
        .select('*')
        .eq('user_email', email)
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (!error && data) {
        transactions = data;
      }
    } else {
      // Obtener las últimas 10 transacciones
      const { data, error } = await supabaseAdmin
        .from('stripe_transactions')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);
      
      if (!error && data) {
        transactions = data;
      }
    }

    // 2. Verificar usuarios creados recientemente
    let recentUsers = [];
    if (email) {
      const { data, error } = await supabaseAdmin
        .from('user_profiles')
        .select('*')
        .eq('email', email);
      
      if (!error && data) {
        recentUsers = data;
      }
    } else {
      const { data, error } = await supabaseAdmin
        .from('user_profiles')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (!error && data) {
        recentUsers = data;
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        transactions,
        recentUsers,
        searchParams: { sessionId, email }
      }
    });

  } catch (error) {
    console.error('Error en debug webhook logs:', error);
    return NextResponse.json({
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
